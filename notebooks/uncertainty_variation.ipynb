#%%
import matplotlib.pyplot as plt
import numpy as np

import os
import sys
import torch
import time
import seaborn as sns
from sklearn.metrics.pairwise import linear_kernel
from scipy.linalg import svd

from sklearn.metrics.pairwise import linear_kernel

sys.path.insert(0,os.path.abspath('/notebooks/3108Dif'))
sys.path.insert(0,os.path.abspath('/notebooks/pytorchtimseries'))
from tqdm.notebook import tqdm



from src.experiments.NsDiff import NsDiffForecast

#%%
from torch_timeseries.dataset import ExchangeRate, ETTh1, ETTh2, ETTm1, ETTm2, Traffic, SolarEnergy, Electricity, ILI
from src.utils.sigma import wv_sigma
dataset_path = "../data/"
#%% md
## ExchangeRate
#%%
dataset = ExchangeRate(root=dataset_path)
#%%
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
#%%
# plt.plot(variance)
#%%
r = (test_variance.mean(0)/train_variance.mean(0))
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%% md
## ETTm1
#%%
dataset = ETTm1(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%%
dataset = ETTm1(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
# variance = wv_sigma(data, 96)[0, :, :]
plt.plot(dataset.data[:, 0])
plt.legend()
#%% md
## ETTm2
#%%
dataset = ETTm2(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%% md
## ETTh1
#%%
dataset = ETTh1(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%% md

#%% md

#%% md
## ETTh2
#%%
dataset = ETTh2(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%% md
### SolarEnergy
#%%
dataset = SolarEnergy(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%%

#%% md
## ILI
#%%
dataset = ILI(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%%
dataset = ILI(root=dataset_path)
data = torch.tensor(dataset.data)
plt.plot(data[:, 2])
#%%

#%% md
## ECL
#%%
dataset = Electricity(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%

#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%% md
## Traffic
#%%
dataset = Traffic(root=dataset_path)
data = torch.tensor(dataset.data).unsqueeze(0)
variance = wv_sigma(data, 96)[0, :, :]
# plt.plot(variance)
#%%
train_variance = variance[:int(len(variance)*0.7)]
test_variance = variance[-int(len(variance)*0.2):]
r = (test_variance.mean(0)/train_variance.mean(0)).max()
r, (test_variance.mean(0)/train_variance.mean(0)).argmax()
#%%

#%% md
