#!/usr/bin/env python3

import fire
from dataclasses import dataclass

@dataclass
class TestClass:
    name: str = "test"
    value: int = 42
    
    def run(self, seed=123):
        print(f"Running with seed: {seed}")
        print(f"Name: {self.name}, Value: {self.value}")
        return {"result": "success", "seed": seed}
    
    def runs(self, seeds=[1, 2, 3]):
        print(f"Running with seeds: {seeds}")
        results = []
        for seed in seeds:
            result = self.run(seed)
            results.append(result)
        return results

if __name__ == "__main__":
    fire.Fire(TestClass)
